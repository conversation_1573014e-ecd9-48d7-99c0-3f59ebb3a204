import { StorageManager } from './StorageManager';
import { safeExists } from './FileUtils';

/**
 * Debug utility to help diagnose download issues
 */

export const DownloadDebugUtils = {
  /**
   * Logs detailed information about all downloaded songs
   */
  logDownloadedSongsInfo: async () => {
    try {
      console.log('=== DOWNLOAD DEBUG INFO ===');
      
      const allMetadata = await StorageManager.getAllDownloadedSongsMetadata();
      const songIds = Object.keys(allMetadata);
      
      console.log(`Total songs in metadata: ${songIds.length}`);
      
      if (songIds.length === 0) {
        console.log('No downloaded songs found in metadata');
        return;
      }
      
      for (const songId of songIds) {
        const metadata = allMetadata[songId];
        const expectedPath = await StorageManager.getSongPath(songId);
        const fileExists = await safeExists(expectedPath);
        
        console.log(`\n--- Song ID: ${songId} ---`);
        console.log(`Title: ${metadata.title}`);
        console.log(`Artist: ${metadata.artist}`);
        console.log(`Expected path: ${expectedPath}`);
        console.log(`File exists: ${fileExists}`);
        console.log(`Local song path in metadata: ${metadata.localSongPath || 'Not set'}`);
        console.log(`Download time: ${metadata.downloadTime ? new Date(metadata.downloadTime).toISOString() : 'Not set'}`);
        
        if (!fileExists) {
          console.warn(`⚠️  File missing for song: ${metadata.title}`);
        } else {
          console.log(`✅ File found for song: ${metadata.title}`);
        }
      }
      
      console.log('=== END DEBUG INFO ===');
    } catch (error) {
      console.error('Error in logDownloadedSongsInfo:', error);
    }
  },

  /**
   * Checks for path mismatches between metadata and expected paths
   */
  checkPathMismatches: async () => {
    try {
      console.log('=== CHECKING PATH MISMATCHES ===');
      
      const allMetadata = await StorageManager.getAllDownloadedSongsMetadata();
      const mismatches = [];
      
      for (const [songId, metadata] of Object.entries(allMetadata)) {
        const expectedPath = await StorageManager.getSongPath(songId);
        const metadataPath = metadata.localSongPath;
        
        if (metadataPath && metadataPath !== expectedPath) {
          mismatches.push({
            songId,
            title: metadata.title,
            expectedPath,
            metadataPath,
            expectedExists: await safeExists(expectedPath),
            metadataExists: await safeExists(metadataPath)
          });
        }
      }
      
      if (mismatches.length > 0) {
        console.log(`Found ${mismatches.length} path mismatches:`);
        mismatches.forEach(mismatch => {
          console.log(`\n--- ${mismatch.title} (${mismatch.songId}) ---`);
          console.log(`Expected: ${mismatch.expectedPath} (exists: ${mismatch.expectedExists})`);
          console.log(`Metadata: ${mismatch.metadataPath} (exists: ${mismatch.metadataExists})`);
        });
      } else {
        console.log('No path mismatches found');
      }
      
      console.log('=== END PATH MISMATCH CHECK ===');
      return mismatches;
    } catch (error) {
      console.error('Error in checkPathMismatches:', error);
      return [];
    }
  },

  /**
   * Attempts to fix path mismatches by updating metadata
   */
  fixPathMismatches: async () => {
    try {
      console.log('=== FIXING PATH MISMATCHES ===');
      
      const mismatches = await DownloadDebugUtils.checkPathMismatches();
      let fixedCount = 0;
      
      for (const mismatch of mismatches) {
        // If the expected path exists but metadata path doesn't, update metadata
        if (mismatch.expectedExists && !mismatch.metadataExists) {
          const allMetadata = await StorageManager.getAllDownloadedSongsMetadata();
          allMetadata[mismatch.songId].localSongPath = mismatch.expectedPath;
          
          await StorageManager.saveDownloadedSongMetadata(mismatch.songId, allMetadata[mismatch.songId]);
          console.log(`✅ Fixed metadata path for: ${mismatch.title}`);
          fixedCount++;
        }
        // If metadata path exists but expected doesn't, the file might need to be moved
        else if (!mismatch.expectedExists && mismatch.metadataExists) {
          console.log(`⚠️  File exists at old location for: ${mismatch.title}`);
          console.log(`   Consider moving from: ${mismatch.metadataPath}`);
          console.log(`   To: ${mismatch.expectedPath}`);
        }
      }
      
      console.log(`Fixed ${fixedCount} path mismatches`);
      console.log('=== END PATH MISMATCH FIX ===');
      return fixedCount;
    } catch (error) {
      console.error('Error in fixPathMismatches:', error);
      return 0;
    }
  }
};
